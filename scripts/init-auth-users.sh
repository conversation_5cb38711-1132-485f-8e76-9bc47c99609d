#!/bin/bash

# GameFlex Auth Users Initialization Script
# This script creates development users using Supabase Auth API
# It runs after all services are up and the auth service is ready

set -e

echo "🔐 Initializing GameFlex development users in Supabase Auth..."

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ .env file not found"
    exit 1
fi

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
until docker compose exec -T db pg_isready -U postgres -h localhost > /dev/null 2>&1; do
    echo "Database is not ready yet, waiting..."
    sleep 2
done

echo "✅ Database is ready!"

# Wait for auth service to be ready
echo "⏳ Waiting for auth service to be ready..."
until curl -s -H "apikey: ${ANON_KEY}" http://localhost:${KONG_HTTP_PORT:-8000}/auth/v1/settings > /dev/null 2>&1; do
    echo "Auth service is not ready yet, waiting..."
    sleep 2
done

echo "✅ Auth service is ready!"

# Function to create a user using direct database insertion with proper GoTrue-compatible hashing
create_auth_user() {
    local user_id="$1"
    local email="$2"
    local password="$3"
    local display_name="$4"

    echo "👤 Creating auth user: $email"

    # Check if user already exists
    local existing_count=$(docker compose exec -T db psql -U postgres -d postgres -c "SELECT COUNT(*) FROM auth.users WHERE email = '$email';" -t | tr -d ' ')

    if [ "$existing_count" -gt 0 ]; then
        echo "   ✅ User $email already exists"
        return 0
    fi

    # Create the user using direct database insertion with proper bcrypt hashing and fixed UUID
    docker compose exec -T db psql -U postgres -d postgres -c "
    INSERT INTO auth.users (
        id,
        instance_id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_app_meta_data,
        raw_user_meta_data,
        is_super_admin,
        role,
        aud,
        confirmation_token,
        recovery_token,
        email_change,
        email_change_token_new,
        email_change_token_current,
        phone_change_token,
        reauthentication_token
    ) VALUES (
        '$user_id',
        '00000000-0000-0000-0000-000000000000',
        '$email',
        crypt('$password', gen_salt('bf', 10)),
        NOW(),
        NOW(),
        NOW(),
        '{\"provider\": \"email\", \"providers\": [\"email\"]}',
        '{\"display_name\": \"$display_name\"}',
        false,
        'authenticated',
        'authenticated',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
    );
    " > /dev/null 2>&1

    # Verify user was created
    local new_count=$(docker compose exec -T db psql -U postgres -d postgres -c "SELECT COUNT(*) FROM auth.users WHERE email = '$email';" -t | tr -d ' ')

    if [ "$new_count" -gt 0 ]; then
        echo "   ✅ Successfully created user: $email"
        return 0
    else
        echo "   ❌ Failed to create user: $email"
        return 1
    fi
}

# Create development users with matching UUIDs from public.users table
echo "🔄 Creating development users..."
create_auth_user "00000000-0000-0000-0000-000000000001" "<EMAIL>" "devpassword123" "Development User"
create_auth_user "00000000-0000-0000-0000-000000000002" "<EMAIL>" "adminpassword123" "Admin User"
create_auth_user "00000000-0000-0000-0000-000000000003" "<EMAIL>" "johnpassword123" "John Doe"
create_auth_user "00000000-0000-0000-0000-000000000004" "<EMAIL>" "janepassword123" "Jane Smith"
create_auth_user "00000000-0000-0000-0000-000000000005" "<EMAIL>" "mikepassword123" "Mike Wilson"

echo ""
echo "🔧 Fixing NULL values in auth.users table for GoTrue compatibility..."
# Fix all nullable string columns that GoTrue v2.174.0+ requires to be empty strings instead of NULL
docker compose exec -T db psql -U postgres -d postgres -c "
UPDATE auth.users SET
  aud = COALESCE(aud, ''),
  role = COALESCE(role, ''),
  confirmation_token = COALESCE(confirmation_token, ''),
  recovery_token = COALESCE(recovery_token, ''),
  email_change_token_new = COALESCE(email_change_token_new, ''),
  email_change = COALESCE(email_change, ''),
  phone_change_token = COALESCE(phone_change_token, ''),
  email_change_token_current = COALESCE(email_change_token_current, ''),
  reauthentication_token = COALESCE(reauthentication_token, '')
WHERE id IS NOT NULL;
" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "   ✅ Successfully fixed NULL values in auth.users table"
else
    echo "   ⚠️  Warning: Could not fix NULL values in auth.users table"
fi

# Verify users were created
echo ""
echo "🔍 Verifying auth users were created..."
user_count=$(curl -s \
    -H "Authorization: Bearer ${SERVICE_ROLE_KEY}" \
    -H "apikey: ${SERVICE_ROLE_KEY}" \
    "http://localhost:${KONG_HTTP_PORT:-8000}/auth/v1/admin/users" | \
    grep -o '"email"' | wc -l)

if [ "$user_count" -ge 5 ]; then
    echo "✅ Successfully created/verified $user_count auth users!"
    echo ""
    echo "🎮 GameFlex Development Users:"
    echo "   📧 <EMAIL> (password: devpassword123)"
    echo "   📧 <EMAIL> (password: adminpassword123)"
    echo "   📧 <EMAIL> (password: johnpassword123)"
    echo "   📧 <EMAIL> (password: janepassword123)"
    echo "   📧 <EMAIL> (password: mikepassword123)"
    echo ""
    echo "🚀 You can now log in to GameFlex with any of these accounts!"
else
    echo "⚠️  Warning: Expected at least 5 users but found $user_count"
fi

echo "✅ Auth users initialization complete!"
