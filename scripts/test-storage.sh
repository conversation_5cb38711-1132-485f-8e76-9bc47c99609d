#!/bin/bash

# Test script to verify storage initialization (Bash)
# This script checks if the media files are accessible via Supabase storage
# Tests the new structured format: user/{user-id}/{channel-id}/{filename}

# Function to show help
show_help() {
    echo -e "\033[32mGameFlex Storage Test Script\033[0m"
    echo -e "\033[33mUsage: ./test-storage.sh\033[0m"
    echo ""
    echo "This script tests if media files are accessible via Supabase storage."
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

echo -e "\033[32m🧪 Testing storage initialization...\033[0m"

# Load environment variables if .env file exists
if [ -f "../.env" ]; then
    export $(cat ../.env | grep -v '^#' | grep -v '^$' | xargs)
elif [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
fi

# Default values if not set
SUPABASE_URL=${SUPABASE_URL:-"http://localhost:8000"}

# List of expected media files with their structured paths
structured_files=(
    "user/00000000-0000-0000-0000-000000000003/10000000-0000-0000-0000-000000000001/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg"
    "user/00000000-0000-0000-0000-000000000004/10000000-0000-0000-0000-000000000003/b2c3d4e5-f6g7-8901-bcde-f23456789012.webp"
    "user/00000000-0000-0000-0000-000000000005/10000000-0000-0000-0000-000000000004/c3d4e5f6-g7h8-9012-cdef-345678901234.webp"
    "user/00000000-0000-0000-0000-000000000001/10000000-0000-0000-0000-000000000002/d4e5f6g7-h8i9-0123-def0-456789012345.png"
)

echo -e "\033[33m🔍 Checking if structured media files are accessible...\033[0m"

success_count=0
total_count=${#structured_files[@]}

for file_path in "${structured_files[@]}"; do
    echo -e "\033[37mTesting: $file_path\033[0m"

    # Test public access to the file using HEAD request
    url="$SUPABASE_URL/storage/v1/object/public/media/$file_path"
    response=$(curl -s -I -w "%{http_code}" -o /dev/null "$url" --max-time 10)

    if [ "$response" = "200" ]; then
        echo -e "\033[32m✅ $file_path is accessible\033[0m"
        success_count=$((success_count + 1))
    else
        echo -e "\033[31m❌ $file_path is not accessible (HTTP $response)\033[0m"
    fi
done

echo ""
echo -e "\033[36m📊 Storage Test Results:\033[0m"
echo -e "\033[37m   Accessible files: $success_count/$total_count\033[0m"

if [ $success_count -eq $total_count ]; then
    echo -e "\033[32m✅ All media files are accessible!\033[0m"
elif [ $success_count -gt 0 ]; then
    echo -e "\033[33m⚠️  Some media files are not accessible\033[0m"
else
    echo -e "\033[31m❌ No media files are accessible\033[0m"
fi

echo -e "\033[32m🏁 Storage test completed!\033[0m"
