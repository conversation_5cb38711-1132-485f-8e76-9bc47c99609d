# GameFlex Backend Scripts

This directory contains utility scripts for the GameFlex backend.

## Storage Initialization

### `init-storage.sh`

This script automatically uploads initial media files from `volumes/storage/media/` to the Supabase storage bucket during container startup. Files are organized in a structured format: `/user/{user-id}/{channel-id}/{random-uuid}.{extension}`.

**What it does:**
- Waits for the Supabase storage service to be ready
- Creates the `media` storage bucket if it doesn't exist
- Uploads files to structured paths based on user and channel ownership
- Uses the Supabase Storage API with proper authentication
- <PERSON>les errors gracefully and continues with other files if one fails

**File Structure:**
Files are organized by user and channel to enable proper access control and organization:
```
media/
└── user/
    ├── {user-id-1}/
    │   └── {channel-id-1}/
    │       └── {random-uuid}.{extension}
    └── {user-id-2}/
        └── {channel-id-2}/
            └── {random-uuid}.{extension}
```

**Files uploaded:**
- `cod_screenshot.jpg` → `user/00000000-0000-0000-0000-000000000003/10000000-0000-0000-0000-000000000001/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg`
- `diablo_screenshot.webp` → `user/00000000-0000-0000-0000-000000000004/10000000-0000-0000-0000-000000000003/b2c3d4e5-f6g7-8901-bcde-f23456789012.webp`
- `minecraft_screenshot.webp` → `user/00000000-0000-0000-0000-000000000005/10000000-0000-0000-0000-000000000004/c3d4e5f6-g7h8-9012-cdef-************.webp`
- `wow_screenshot.png` → `user/00000000-0000-0000-0000-000000000001/10000000-0000-0000-0000-000000000002/d4e5f6g7-h8i9-0123-def0-************.png`

**Usage:**
The script runs automatically when you start the backend with `docker-compose up`. It's configured as a service called `storage-init` in the docker-compose.yml file.

### `test-storage.sh`

This script tests if the storage initialization was successful by checking if the media files are accessible via the Supabase storage API.

**Usage:**
```bash
./scripts/test-storage.sh
```

**What it checks:**
- Public accessibility of each media file
- Returns HTTP status codes for each file
- Confirms the storage bucket is working correctly

## Troubleshooting

### Storage initialization failed
1. Check the logs: `docker-compose logs storage-init`
2. Verify the storage service is healthy: `docker-compose ps storage`
3. Test manually: `./scripts/test-storage.sh`

### Files not accessible
1. Ensure the storage bucket was created (check database logs)
2. Verify the storage policies are correct
3. Check if the files were uploaded: `docker-compose logs storage-init`

### Re-running initialization
If you need to re-run the storage initialization:
```bash
docker-compose restart storage-init
```

Or to completely reset and re-initialize:
```bash
docker-compose down -v
./start.sh
```
