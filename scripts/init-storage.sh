#!/bin/sh

# Storage initialization script for GameFlex
# This script uploads initial media files to the Supabase storage bucket
# Files are organized by user and channel: /user/{user-id}/{channel-id}/{random-uuid}.{extension}

set -e

echo "🚀 Starting storage initialization..."

# Wait for storage service to be fully ready
echo "⏳ Waiting for storage service to be ready..."
sleep 15

# Define the mapping of files to users and channels (matching seed data)
# Format: "filename:user_id:channel_id:new_filename"
FILE_MAPPINGS="
cod_screenshot.jpg:00000000-0000-0000-0000-000000000003:10000000-0000-0000-0000-000000000001:a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg
diablo_screenshot.webp:00000000-0000-0000-0000-000000000004:10000000-0000-0000-0000-000000000003:b2c3d4e5-f6g7-8901-bcde-f23456789012.webp
minecraft_screenshot.webp:00000000-0000-0000-0000-000000000005:10000000-0000-0000-0000-000000000004:c3d4e5f6-g7h8-9012-cdef-************.webp
wow_screenshot.png:00000000-0000-0000-0000-000000000001:10000000-0000-0000-0000-000000000002:d4e5f6g7-h8i9-0123-def0-************.png
AI_Game.mp4:00000000-0000-0000-0000-000000000002:10000000-0000-0000-0000-000000000001:e5f6g7h8-i9j0-1234-efgh-************.mp4
"

# Function to check if storage is ready
check_storage_ready() {
    local max_attempts=60  # Increased from 30 to 60
    local attempt=1

    echo "🔍 Checking storage service readiness..."
    echo "   SUPABASE_URL: $SUPABASE_URL"
    echo "   SERVICE_KEY length: ${#SUPABASE_SERVICE_KEY}"

    while [ $attempt -le $max_attempts ]; do
        echo "🔍 Checking storage readiness (attempt $attempt/$max_attempts)..."

        # First check if we can reach the storage endpoint at all
        if curl -s --max-time 5 "$SUPABASE_URL/storage/v1/bucket" > /dev/null 2>&1; then
            echo "   Storage endpoint is reachable"

            # Then check with authentication
            response=$(curl -s -w "\n%{http_code}" \
                -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
                "$SUPABASE_URL/storage/v1/bucket" 2>/dev/null)

            http_code=$(echo "$response" | tail -n1)
            response_body=$(echo "$response" | head -n -1)

            if [ "$http_code" = "200" ]; then
                echo "✅ Storage service is ready!"
                return 0
            else
                echo "   Storage responded with HTTP $http_code"
                if [ -n "$response_body" ]; then
                    echo "   Response: $response_body"
                fi
            fi
        else
            echo "   Storage endpoint not reachable yet..."
        fi

        echo "⏳ Storage not ready yet, waiting..."
        sleep 3  # Increased from 2 to 3 seconds
        attempt=$((attempt + 1))
    done

    echo "❌ Storage service failed to become ready after $max_attempts attempts"
    echo "   Final check details:"
    echo "   URL: $SUPABASE_URL/storage/v1/bucket"
    echo "   Service key: ${SUPABASE_SERVICE_KEY:0:20}..."
    return 1
}

# Function to create storage bucket if it doesn't exist
create_bucket() {
    local bucket="media"

    echo "🪣 Checking if bucket '$bucket' exists..."

    # Check if bucket exists
    response=$(curl -s -w "\n%{http_code}" --max-time 10 \
        -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
        "$SUPABASE_URL/storage/v1/bucket/$bucket")

    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ]; then
        echo "✅ Bucket '$bucket' already exists"
        return 0
    elif [ "$http_code" = "404" ]; then
        echo "📦 Bucket '$bucket' does not exist, creating..."
    else
        echo "⚠️  Unexpected response when checking bucket (HTTP $http_code)"
        echo "Response: $response_body"
        echo "Proceeding to create bucket anyway..."
    fi

    echo "📦 Creating bucket '$bucket'..."

    # Create bucket with proper JSON formatting
    bucket_config='{
        "id": "media",
        "name": "media",
        "public": true,
        "file_size_limit": 52428800,
        "allowed_mime_types": ["image/jpeg", "image/png", "image/webp", "image/gif", "video/mp4", "video/webm"]
    }'

    response=$(curl -s -w "\n%{http_code}" --max-time 15 \
        -X POST \
        -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
        -H "Content-Type: application/json" \
        -d "$bucket_config" \
        "$SUPABASE_URL/storage/v1/bucket")

    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo "✅ Successfully created bucket '$bucket'"
        return 0
    else
        echo "❌ Failed to create bucket '$bucket' (HTTP $http_code)"
        echo "Request URL: $SUPABASE_URL/storage/v1/bucket"
        echo "Request body: $bucket_config"
        echo "Response: $response_body"

        # Check if it's an authorization issue
        if [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
            echo "🔑 Authorization issue detected. Check SERVICE_ROLE_KEY."
        fi

        return 1
    fi
}

# Function to get MIME type based on file extension
get_mime_type() {
    local file_name="$1"
    local extension="${file_name##*.}"

    # Convert to lowercase using tr
    extension=$(echo "$extension" | tr '[:upper:]' '[:lower:]')

    case "$extension" in
        jpg|jpeg) echo "image/jpeg" ;;
        png) echo "image/png" ;;
        webp) echo "image/webp" ;;
        gif) echo "image/gif" ;;
        mp4) echo "video/mp4" ;;
        webm) echo "video/webm" ;;
        *) echo "application/octet-stream" ;;
    esac
}

# Function to upload a file to storage with user/channel structure
upload_file_structured() {
    local file_path="$1"
    local user_id="$2"
    local channel_id="$3"
    local new_filename="$4"
    local bucket="media"

    # Create the structured path: user/{user-id}/{channel-id}/{filename}
    local storage_path="user/$user_id/$channel_id/$new_filename"

    echo "📤 Uploading $new_filename to $storage_path..."
    echo "   Source file: $file_path"
    echo "   File size: $(wc -c < "$file_path") bytes"

    # Check if source file exists and is readable
    if [ ! -f "$file_path" ]; then
        echo "❌ Source file does not exist: $file_path"
        return 1
    fi

    if [ ! -r "$file_path" ]; then
        echo "❌ Source file is not readable: $file_path"
        return 1
    fi

    # Get MIME type based on file extension
    mime_type=$(get_mime_type "$new_filename")
    echo "   MIME type: $mime_type"

    # Upload file using Supabase Storage API
    response=$(curl -s -w "\n%{http_code}" --max-time 30 \
        -X POST \
        -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
        -H "Content-Type: $mime_type" \
        -H "x-upsert: true" \
        --data-binary "@$file_path" \
        "$SUPABASE_URL/storage/v1/object/$bucket/$storage_path")

    # Extract HTTP code from last line
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo "✅ Successfully uploaded $storage_path (MIME: $mime_type)"

        # Verify the upload by checking if the file is accessible
        verify_response=$(curl -s -I -w "%{http_code}" -o /dev/null --max-time 10 \
            "$SUPABASE_URL/storage/v1/object/public/$bucket/$storage_path")

        if [ "$verify_response" = "200" ]; then
            echo "   ✅ Upload verified - file is publicly accessible"
        else
            echo "   ⚠️  Upload completed but file verification failed (HTTP $verify_response)"
        fi
    else
        echo "❌ Failed to upload $storage_path (HTTP $http_code)"
        echo "   Request URL: $SUPABASE_URL/storage/v1/object/$bucket/$storage_path"
        echo "   MIME type: $mime_type"
        echo "   Response: $response_body"

        # Check for common error conditions
        if [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
            echo "   🔑 Authorization issue - check SERVICE_ROLE_KEY"
        elif [ "$http_code" = "413" ]; then
            echo "   📏 File too large - check file size limits"
        elif [ "$http_code" = "415" ]; then
            echo "   📄 Unsupported media type - check MIME type and allowed types"
        fi

        # Don't exit on failure, continue with other files
        return 1
    fi
}

# Check if storage service is ready
if ! check_storage_ready; then
    echo "❌ Cannot proceed without storage service"
    exit 1
fi

# Create storage bucket if it doesn't exist
if ! create_bucket; then
    echo "❌ Cannot proceed without storage bucket"
    exit 1
fi

# Check if media directory exists
if [ ! -d "/media" ]; then
    echo "❌ Media directory not found at /media"
    exit 1
fi

# Upload all media files using the structured approach
echo "📁 Checking media directory contents:"
if [ -d "/media" ]; then
    ls -la /media/
    echo "   Total files in /media: $(find /media -type f | wc -l)"
else
    echo "❌ /media directory not found!"
    exit 1
fi

echo ""
echo "🚀 Starting file uploads..."

file_count=0
success_count=0
failed_count=0

# Process each file mapping
echo "$FILE_MAPPINGS" | while IFS= read -r line; do
    # Skip empty lines
    [ -z "$line" ] && continue

    # Parse the mapping: filename:user_id:channel_id:new_filename
    original_filename=$(echo "$line" | cut -d: -f1)
    user_id=$(echo "$line" | cut -d: -f2)
    channel_id=$(echo "$line" | cut -d: -f3)
    new_filename=$(echo "$line" | cut -d: -f4)

    file_path="/media/$original_filename"

    echo ""
    echo "🔄 Processing file $((file_count + 1)): $original_filename"
    echo "   Target path: user/$user_id/$channel_id/$new_filename"

    if [ -f "$file_path" ]; then
        if upload_file_structured "$file_path" "$user_id" "$channel_id" "$new_filename"; then
            success_count=$((success_count + 1))
            echo "   ✅ Upload successful"
        else
            failed_count=$((failed_count + 1))
            echo "   ❌ Upload failed"
        fi
        file_count=$((file_count + 1))
    else
        echo "   ⚠️  Source file not found: $original_filename"
        failed_count=$((failed_count + 1))
    fi
done

echo ""
echo "📊 Storage initialization summary:"
echo "   Total files processed: $file_count"
echo "   Successful uploads: $success_count"
echo "   Failed uploads: $failed_count"

if [ $success_count -eq $file_count ] && [ $file_count -gt 0 ]; then
    echo "✅ Storage initialization completed successfully!"
    exit_code=0
elif [ $success_count -gt 0 ]; then
    echo "⚠️  Storage initialization completed with some failures"
    exit_code=1
else
    echo "❌ Storage initialization failed - no files uploaded successfully"
    exit_code=1
fi

# Keep container running for a moment to see logs
echo "⏳ Keeping container alive for 10 seconds to view logs..."
sleep 10

exit $exit_code
