-- Storage policies for media bucket
-- This file creates the necessary RLS policies for the media storage bucket

-- Wait for storage schema to be ready and create the media bucket if it doesn't exist
DO $$
BEGIN
    -- Check if storage.buckets table exists and has the expected columns
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'storage' AND table_name = 'buckets'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'storage' AND table_name = 'buckets' AND column_name = 'public'
    ) THEN
        -- Insert the media bucket
        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, avif_autodetection)
        VALUES (
            'media',
            'media',
            true,
            52428800, -- 50MB limit
            ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/webm'],
            false
        )
        ON CONFLICT (id) DO UPDATE SET
            public = EXCLUDED.public,
            file_size_limit = EXCLUDED.file_size_limit,
            allowed_mime_types = EXCLUDED.allowed_mime_types,
            avif_autodetection = EXCLUDED.avif_autodetection;

        RAISE NOTICE 'Media bucket created/updated successfully';
    ELSE
        RAISE WARNING 'Storage schema not ready or missing expected columns, skipping bucket creation';
    END IF;
END $$;

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Allow public read access to media files
CREATE POLICY "Media files are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'media');

-- Policy: Allow authenticated users to upload media files
CREATE POLICY "Authenticated users can upload media files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'media' 
        AND auth.role() = 'authenticated'
    );

-- Policy: Allow users to update their own media files
CREATE POLICY "Users can update their own media files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'media' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    ) WITH CHECK (
        bucket_id = 'media' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Policy: Allow users to delete their own media files
CREATE POLICY "Users can delete their own media files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'media' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Drop existing conflicting policies if they exist
DROP POLICY IF EXISTS "Media images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload media" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can update media" ON storage.objects;

-- Log completion
SELECT 'Storage policies for media bucket have been successfully applied' AS status;
