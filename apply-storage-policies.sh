#!/bin/bash

# Apply storage policies after storage service is ready
# Shell script version of apply-storage-policies.ps1

echo -e "\033[33m📦 Applying storage policies...\033[0m"

# Determine Docker Compose command (prefer new syntax)
if command -v docker > /dev/null 2>&1 && docker compose version > /dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose > /dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo -e "\033[31m❌ Docker Compose is not available.\033[0m"
    exit 1
fi

# Wait for storage service to be healthy
max_attempts=30
attempt=0
storage_ready=false

while [ $attempt -lt $max_attempts ] && [ "$storage_ready" = false ]; do
    attempt=$((attempt + 1))
    echo -e "\033[37m   Checking storage service health (attempt $attempt/$max_attempts)...\033[0m"
    
    # Check storage service status
    storage_status=$($DOCKER_COMPOSE_CMD ps storage --format json 2>/dev/null || echo "")
    if [ -n "$storage_status" ]; then
        if echo "$storage_status" | grep -q '"Health":"healthy"'; then
            storage_ready=true
            echo -e "\033[32m✅ Storage service is healthy\033[0m"
        else
            health_status=$(echo "$storage_status" | grep -o '"Health":"[^"]*"' | cut -d'"' -f4 || echo "unknown")
            echo -e "\033[37m   Storage service status: $health_status\033[0m"
            sleep 2
        fi
    else
        echo -e "\033[37m   Storage service not ready yet...\033[0m"
        sleep 2
    fi
done

if [ "$storage_ready" = false ]; then
    echo -e "\033[33m⚠️  Storage service did not become healthy within timeout\033[0m"
    echo -e "\033[37m   Skipping storage policies application\033[0m"
    exit 0
fi

# Apply storage policies
echo -e "\033[37m   Applying storage bucket and policies...\033[0m"

# Create a temporary SQL file with the storage policies
temp_sql_file=$(mktemp --suffix=.sql)

cat > "$temp_sql_file" << 'EOF'
-- Storage policies for media bucket
-- This file creates the necessary RLS policies for the media storage bucket

-- Wait for storage schema to be ready and create the media bucket if it doesn't exist
DO $$
BEGIN
    -- Check if storage.buckets table exists and has the expected columns
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'storage' AND table_name = 'buckets'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'storage' AND table_name = 'buckets' AND column_name = 'public'
    ) THEN
        -- Insert the media bucket
        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types, avif_autodetection)
        VALUES (
            'media',
            'media',
            true,
            52428800, -- 50MB limit
            ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/webm'],
            false
        )
        ON CONFLICT (id) DO UPDATE SET
            public = EXCLUDED.public,
            file_size_limit = EXCLUDED.file_size_limit,
            allowed_mime_types = EXCLUDED.allowed_mime_types,
            avif_autodetection = EXCLUDED.avif_autodetection;

        RAISE NOTICE 'Media bucket created/updated successfully';
    ELSE
        RAISE WARNING 'Storage schema not ready or missing expected columns, skipping bucket creation';
    END IF;
END $$;

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Allow public read access to media files
CREATE POLICY "Media files are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'media');

-- Policy: Allow authenticated users to upload media files
CREATE POLICY "Authenticated users can upload media files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'media'
        AND auth.role() = 'authenticated'
    );

-- Policy: Allow users to update their own media files
CREATE POLICY "Users can update their own media files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'media'
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Policy: Allow users to delete their own media files
CREATE POLICY "Users can delete their own media files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'media'
        AND auth.uid()::text = (storage.foldername(name))[1]
    );
EOF

# Copy the file to the container and execute it
if docker cp "$temp_sql_file" supabase-db:/tmp/storage_policies.sql > /dev/null 2>&1; then
    result=$($DOCKER_COMPOSE_CMD exec -T db psql -U postgres -d postgres -f /tmp/storage_policies.sql 2>&1)
    exit_code=$?
    
    # Clean up the temporary file
    rm -f "$temp_sql_file"
    
    if [ $exit_code -eq 0 ]; then
        echo -e "\033[32m✅ Storage policies applied successfully\033[0m"
    else
        echo -e "\033[33m⚠️  Some storage policies may have failed to apply\033[0m"
        echo -e "\033[37m   SQL output: $result\033[0m"
    fi
else
    echo -e "\033[31m❌ Failed to copy SQL file to container\033[0m"
    rm -f "$temp_sql_file"
    exit 1
fi
